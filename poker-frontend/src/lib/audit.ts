export interface AuditLogData {
  adminEmail: string;
  action: string;
  target: string;
  details?: string;
}

export async function logAdminAction(data: AuditLogData): Promise<void> {
  try {
    await fetch('/api/admin/audit', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
  } catch (error) {
    console.error('Failed to log admin action:', error);
  }
}

// Predefined action types for consistency
export const AUDIT_ACTIONS = {
  USER_DISABLE: 'USER_DISABLE',
  USER_ENABLE: 'USER_ENABLE',
  TABLE_CREATE: 'TABLE_CREATE',
  TABLE_UPDATE: 'TABLE_UPDATE',
  TABLE_DELETE: 'TABLE_DELETE',
  SETTINGS_UPDATE: 'SETTINGS_UPDATE',
  SETTINGS_RESET: 'SETTINGS_RESET',
  SERVER_RESTART: 'SERVER_RESTART',
  AUDIT_CLEAR: 'AUDIT_CLEAR',
  LOGIN: 'ADMIN_LOGIN',
  LOGOUT: 'ADMIN_LOGOUT',
} as const;

export type AuditAction = typeof AUDIT_ACTIONS[keyof typeof AUDIT_ACTIONS];
