import { NextRequest, NextResponse } from 'next/server';
import { getRedisClient } from '@/lib/redis';

interface AuditLog {
  id: string;
  timestamp: string;
  adminEmail: string;
  action: string;
  target: string;
  details: string;
  ipAddress?: string;
  userAgent?: string;
}

export async function GET(request: NextRequest) {
  try {
    const redis = await getRedisClient();
    
    // Get audit logs from Redis (stored as a sorted set by timestamp)
    const logs = await redis.zRevRange('admin_audit_logs', 0, 99); // Get last 100 logs
    
    const auditLogs: AuditLog[] = [];
    for (const logData of logs) {
      try {
        const log = JSON.parse(logData);
        auditLogs.push(log);
      } catch (e) {
        console.error('Failed to parse audit log:', e);
      }
    }

    return NextResponse.json({ logs: auditLogs });
  } catch (error) {
    console.error('Failed to fetch audit logs:', error);
    return NextResponse.json({ error: 'Failed to fetch audit logs' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { adminEmail, action, target, details } = body;

    if (!adminEmail || !action || !target) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    const redis = await getRedisClient();
    
    const auditLog: AuditLog = {
      id: `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      adminEmail,
      action,
      target,
      details: details || '',
      ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
    };

    // Store in Redis sorted set with timestamp as score
    await redis.zAdd('admin_audit_logs', {
      score: Date.now(),
      value: JSON.stringify(auditLog),
    });

    // Keep only the last 1000 audit logs
    await redis.zRemRangeByRank('admin_audit_logs', 0, -1001);

    return NextResponse.json({ success: true, log: auditLog });
  } catch (error) {
    console.error('Failed to create audit log:', error);
    return NextResponse.json({ error: 'Failed to create audit log' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    if (action === 'clear_all') {
      const redis = await getRedisClient();
      await redis.del('admin_audit_logs');
      
      return NextResponse.json({ message: 'All audit logs cleared successfully' });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
  } catch (error) {
    console.error('Failed to clear audit logs:', error);
    return NextResponse.json({ error: 'Failed to clear audit logs' }, { status: 500 });
  }
}
