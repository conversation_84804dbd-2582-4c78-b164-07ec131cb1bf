import { NextRequest, NextResponse } from 'next/server';
import { getRedisClient } from '@/lib/redis';

interface AnalyticsData {
  totalUsers: number;
  activeUsers: number;
  activeTables: number;
  totalTables: number;
  gamesPlayed: number;
  totalPot: number;
  avgGameDuration: number;
  peakConcurrentUsers: number;
  userGrowth: {
    daily: number;
    weekly: number;
    monthly: number;
  };
  tableUtilization: {
    sixPlayer: {
      total: number;
      active: number;
      utilization: number;
    };
    ninePlayer: {
      total: number;
      active: number;
      utilization: number;
    };
  };
  recentActivity: Array<{
    timestamp: string;
    type: 'user_join' | 'user_leave' | 'game_start' | 'game_end' | 'table_create';
    description: string;
  }>;
}

export async function GET(request: NextRequest) {
  try {
    // Get active sessions from Redis
    const redis = await getRedisClient();
    const sessionKeys = await redis.keys('session:*');
    
    const activeUsers = sessionKeys.length;
    
    // Get unique users (remove duplicates by email)
    const uniqueUsers = new Set();
    for (const key of sessionKeys) {
      const sessionData = await redis.get(key);
      if (sessionData) {
        const session = JSON.parse(sessionData);
        uniqueUsers.add(session.user_email);
      }
    }

    // Mock data for other analytics
    // In a real implementation, you would query your database for this information
    const analyticsData: AnalyticsData = {
      totalUsers: uniqueUsers.size,
      activeUsers: activeUsers,
      activeTables: 2, // Mock: would come from table service
      totalTables: 3, // Mock: would come from database
      gamesPlayed: 156, // Mock: would come from games table
      totalPot: 125000, // Mock: sum of all pots
      avgGameDuration: 18.5, // Mock: average in minutes
      peakConcurrentUsers: Math.max(activeUsers, 8), // Mock: daily peak
      userGrowth: {
        daily: 2, // Mock: new users today
        weekly: 12, // Mock: new users this week
        monthly: 45, // Mock: new users this month
      },
      tableUtilization: {
        sixPlayer: {
          total: 2,
          active: 1,
          utilization: 50,
        },
        ninePlayer: {
          total: 1,
          active: 1,
          utilization: 100,
        },
      },
      recentActivity: [
        {
          timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
          type: 'user_join',
          description: 'User joined table HIGH001',
        },
        {
          timestamp: new Date(Date.now() - 12 * 60 * 1000).toISOString(),
          type: 'game_end',
          description: 'Game completed on table BEGIN001 - Pot: $2,500',
        },
        {
          timestamp: new Date(Date.now() - 18 * 60 * 1000).toISOString(),
          type: 'user_leave',
          description: 'User left table HIGH001',
        },
        {
          timestamp: new Date(Date.now() - 25 * 60 * 1000).toISOString(),
          type: 'game_start',
          description: 'New game started on table HIGH001',
        },
        {
          timestamp: new Date(Date.now() - 35 * 60 * 1000).toISOString(),
          type: 'table_create',
          description: 'New table created: PRIV001',
        },
      ],
    };

    return NextResponse.json(analyticsData);
  } catch (error) {
    console.error('Failed to fetch analytics:', error);
    return NextResponse.json({ error: 'Failed to fetch analytics' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, data } = body;

    // Handle different analytics actions
    switch (action) {
      case 'reset_stats':
        // In a real implementation, you might reset certain statistics
        return NextResponse.json({ message: 'Statistics reset successfully' });
      
      case 'export_data':
        // In a real implementation, you might generate and return a data export
        return NextResponse.json({ 
          message: 'Data export initiated',
          downloadUrl: '/api/admin/analytics/export/latest.csv' // Mock URL
        });
      
      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Failed to perform analytics action:', error);
    return NextResponse.json({ error: 'Failed to perform action' }, { status: 500 });
  }
}
