import { NextRequest, NextResponse } from 'next/server';
import { getRedisClient } from '@/lib/redis';

interface AnalyticsData {
  totalUsers: number;
  activeUsers: number;
  activeTables: number;
  totalTables: number;
  gamesPlayed: number;
  totalPot: number;
  avgGameDuration: number;
  peakConcurrentUsers: number;
  userGrowth: {
    daily: number;
    weekly: number;
    monthly: number;
  };
  tableUtilization: {
    sixPlayer: {
      total: number;
      active: number;
      utilization: number;
    };
    ninePlayer: {
      total: number;
      active: number;
      utilization: number;
    };
  };
  recentActivity: Array<{
    timestamp: string;
    type: 'user_join' | 'user_leave' | 'game_start' | 'game_end' | 'table_create';
    description: string;
  }>;
}

export async function GET(request: NextRequest) {
  try {
    // Get active sessions from Redis
    const redis = await getRedisClient();
    const sessionKeys = await redis.keys('session:*');
    
    const activeUsers = sessionKeys.length;
    
    // Get unique users (remove duplicates by email)
    const uniqueUsers = new Set();
    for (const key of sessionKeys) {
      const sessionData = await redis.get(key);
      if (sessionData) {
        const session = JSON.parse(sessionData);
        uniqueUsers.add(session.user_email);
      }
    }

    // Get table data from query parameter if provided (from admin page)
    const tablesParam = request.nextUrl.searchParams.get('tables');
    let tableData = { activeTables: 0, totalTables: 0, totalPot: 0, gamesPlayed: 0 };

    if (tablesParam) {
      try {
        const tables = JSON.parse(tablesParam);
        tableData = {
          activeTables: tables.filter((t: any) => t.seated > 0).length,
          totalTables: tables.length,
          totalPot: tables.reduce((sum: number, t: any) => sum + (t.totalPot || 0), 0),
          gamesPlayed: tables.reduce((sum: number, t: any) => sum + (t.totalHands || 0), 0),
        };
      } catch (e) {
        console.error('Failed to parse tables data:', e);
      }
    }

    const analyticsData: AnalyticsData = {
      totalUsers: uniqueUsers.size,
      activeUsers: activeUsers,
      activeTables: tableData.activeTables,
      totalTables: tableData.totalTables,
      gamesPlayed: tableData.gamesPlayed,
      totalPot: tableData.totalPot,
      avgGameDuration: tableData.gamesPlayed > 0 ? Math.round((tableData.totalPot / tableData.gamesPlayed) / 100) : 0, // Rough estimate
      peakConcurrentUsers: Math.max(activeUsers, 8), // Mock: daily peak
      userGrowth: {
        daily: 2, // Mock: new users today
        weekly: 12, // Mock: new users this week
        monthly: 45, // Mock: new users this month
      },
      tableUtilization: (() => {
        if (tablesParam) {
          try {
            const tables = JSON.parse(tablesParam);
            const sixPlayerTables = tables.filter((t: any) => t.type === '6p');
            const ninePlayerTables = tables.filter((t: any) => t.type === '9p');
            const activeSixPlayer = sixPlayerTables.filter((t: any) => t.seated > 0).length;
            const activeNinePlayer = ninePlayerTables.filter((t: any) => t.seated > 0).length;

            return {
              sixPlayer: {
                total: sixPlayerTables.length,
                active: activeSixPlayer,
                utilization: sixPlayerTables.length > 0 ? Math.round((activeSixPlayer / sixPlayerTables.length) * 100) : 0,
              },
              ninePlayer: {
                total: ninePlayerTables.length,
                active: activeNinePlayer,
                utilization: ninePlayerTables.length > 0 ? Math.round((activeNinePlayer / ninePlayerTables.length) * 100) : 0,
              },
            };
          } catch (e) {
            console.error('Failed to parse tables for utilization:', e);
          }
        }

        // Fallback to mock data
        return {
          sixPlayer: {
            total: 2,
            active: 1,
            utilization: 50,
          },
          ninePlayer: {
            total: 1,
            active: 1,
            utilization: 100,
          },
        };
      })(),
      recentActivity: [
        {
          timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
          type: 'user_join',
          description: 'User joined table HIGH001',
        },
        {
          timestamp: new Date(Date.now() - 12 * 60 * 1000).toISOString(),
          type: 'game_end',
          description: 'Game completed on table BEGIN001 - Pot: $2,500',
        },
        {
          timestamp: new Date(Date.now() - 18 * 60 * 1000).toISOString(),
          type: 'user_leave',
          description: 'User left table HIGH001',
        },
        {
          timestamp: new Date(Date.now() - 25 * 60 * 1000).toISOString(),
          type: 'game_start',
          description: 'New game started on table HIGH001',
        },
        {
          timestamp: new Date(Date.now() - 35 * 60 * 1000).toISOString(),
          type: 'table_create',
          description: 'New table created: PRIV001',
        },
      ],
    };

    return NextResponse.json(analyticsData);
  } catch (error) {
    console.error('Failed to fetch analytics:', error);
    return NextResponse.json({ error: 'Failed to fetch analytics' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, data } = body;

    // Handle different analytics actions
    switch (action) {
      case 'reset_stats':
        // In a real implementation, you might reset certain statistics
        return NextResponse.json({ message: 'Statistics reset successfully' });
      
      case 'export_data':
        // In a real implementation, you might generate and return a data export
        return NextResponse.json({ 
          message: 'Data export initiated',
          downloadUrl: '/api/admin/analytics/export/latest.csv' // Mock URL
        });
      
      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Failed to perform analytics action:', error);
    return NextResponse.json({ error: 'Failed to perform action' }, { status: 500 });
  }
}
