import { NextRequest, NextResponse } from 'next/server';

interface ServerSettings {
  game: {
    defaultBigBlind: number;
    defaultSmallBlind: number;
    defaultMinBuyIn: number;
    defaultMaxBuyIn: number;
    maxTablesPerUser: number;
    gameTimeoutMinutes: number;
    autoStartDelay: number;
    showdownTimeout: number;
  };
  table: {
    maxWaitingListSize: number;
    defaultTableExpiry: number; // hours
    allowPrivateTables: boolean;
    maxPrivateTablesPerUser: number;
  };
  server: {
    maxConcurrentUsers: number;
    sessionTimeoutHours: number;
    enableRegistration: boolean;
    maintenanceMode: boolean;
    debugMode: boolean;
  };
  security: {
    requireEmailVerification: boolean;
    maxLoginAttempts: number;
    lockoutDurationMinutes: number;
    enableRateLimiting: boolean;
  };
  notifications: {
    enableEmailNotifications: boolean;
    enablePushNotifications: boolean;
    gameStartNotifications: boolean;
    tournamentNotifications: boolean;
  };
}

export async function GET(request: NextRequest) {
  try {
    // In a real implementation, you would fetch these from your configuration store
    // For now, return mock settings
    const settings: ServerSettings = {
      game: {
        defaultBigBlind: 100,
        defaultSmallBlind: 50,
        defaultMinBuyIn: 1000,
        defaultMaxBuyIn: 10000,
        maxTablesPerUser: 3,
        gameTimeoutMinutes: 30,
        autoStartDelay: 10,
        showdownTimeout: 15,
      },
      table: {
        maxWaitingListSize: 10,
        defaultTableExpiry: 24,
        allowPrivateTables: true,
        maxPrivateTablesPerUser: 2,
      },
      server: {
        maxConcurrentUsers: 1000,
        sessionTimeoutHours: 24,
        enableRegistration: true,
        maintenanceMode: false,
        debugMode: false,
      },
      security: {
        requireEmailVerification: false,
        maxLoginAttempts: 5,
        lockoutDurationMinutes: 15,
        enableRateLimiting: true,
      },
      notifications: {
        enableEmailNotifications: true,
        enablePushNotifications: false,
        gameStartNotifications: true,
        tournamentNotifications: true,
      },
    };

    return NextResponse.json({ settings });
  } catch (error) {
    console.error('Failed to fetch settings:', error);
    return NextResponse.json({ error: 'Failed to fetch settings' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { settings } = body;

    if (!settings) {
      return NextResponse.json({ error: 'Settings object is required' }, { status: 400 });
    }

    // Validate critical settings
    if (settings.game) {
      const { defaultBigBlind, defaultSmallBlind, defaultMinBuyIn, defaultMaxBuyIn } = settings.game;
      
      if (defaultBigBlind && defaultSmallBlind && defaultBigBlind <= defaultSmallBlind) {
        return NextResponse.json({ 
          error: 'Default big blind must be greater than small blind' 
        }, { status: 400 });
      }

      if (defaultMaxBuyIn && defaultMinBuyIn && defaultMaxBuyIn <= defaultMinBuyIn) {
        return NextResponse.json({ 
          error: 'Default max buy-in must be greater than min buy-in' 
        }, { status: 400 });
      }
    }

    if (settings.server) {
      const { maxConcurrentUsers, sessionTimeoutHours } = settings.server;
      
      if (maxConcurrentUsers && maxConcurrentUsers < 1) {
        return NextResponse.json({ 
          error: 'Max concurrent users must be at least 1' 
        }, { status: 400 });
      }

      if (sessionTimeoutHours && sessionTimeoutHours < 1) {
        return NextResponse.json({ 
          error: 'Session timeout must be at least 1 hour' 
        }, { status: 400 });
      }
    }

    // In a real implementation, you would:
    // 1. Validate all settings thoroughly
    // 2. Update the configuration store (database, config files, etc.)
    // 3. Notify the Hamster server to reload configuration
    // 4. Log the configuration change for audit purposes

    return NextResponse.json({ 
      message: 'Settings updated successfully',
      settings: settings // Return the updated settings
    });
  } catch (error) {
    console.error('Failed to update settings:', error);
    return NextResponse.json({ error: 'Failed to update settings' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action } = body;

    switch (action) {
      case 'reset_to_defaults':
        // Reset all settings to default values
        return NextResponse.json({ message: 'Settings reset to defaults successfully' });
      
      case 'backup_settings':
        // Create a backup of current settings
        return NextResponse.json({ 
          message: 'Settings backup created successfully',
          backupId: `backup_${Date.now()}`
        });
      
      case 'restart_server':
        // In a real implementation, this would trigger a graceful server restart
        return NextResponse.json({ message: 'Server restart initiated' });
      
      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Failed to perform settings action:', error);
    return NextResponse.json({ error: 'Failed to perform action' }, { status: 500 });
  }
}
