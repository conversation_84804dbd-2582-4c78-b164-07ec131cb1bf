import { NextRequest, NextResponse } from 'next/server';
import { getRedisClient } from '@/lib/redis';

export async function GET(request: NextRequest) {
  try {
    // Get all active sessions from Redis to build user list
    const redis = await getRedisClient();
    const sessionKeys = await redis.keys('session:*');

    const users = [];
    for (const key of sessionKeys) {
      const sessionData = await redis.get(key);
      if (sessionData) {
        const session = JSON.parse(sessionData);
        users.push({
          uid: session.user_id,
          email: session.user_email,
          displayName: session.display_name || session.user_email,
          creationTime: session.created_at,
          lastSignInTime: session.created_at, // For now, use creation time
          disabled: false, // Sessions in Redis are active by definition
          sessionToken: key.replace('session:', ''),
        });
      }
    }

    // Remove duplicates by email (in case of multiple sessions)
    const uniqueUsers = users.reduce((acc, user) => {
      const existing = acc.find(u => u.email === user.email);
      if (!existing) {
        acc.push(user);
      } else if (new Date(user.creationTime) > new Date(existing.creationTime)) {
        // Keep the more recent session
        const index = acc.indexOf(existing);
        acc[index] = user;
      }
      return acc;
    }, []);

    return NextResponse.json({ users: uniqueUsers });
  } catch (error) {
    console.error('Failed to fetch users:', error);
    return NextResponse.json({ error: 'Failed to fetch users' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, userId, email } = body;

    if (action === 'disable' && (userId || email)) {
      // Disable user by removing their session
      const redis = await getRedisClient();
      const sessionKeys = await redis.keys('session:*');

      for (const key of sessionKeys) {
        const sessionData = await redis.get(key);
        if (sessionData) {
          const session = JSON.parse(sessionData);
          if (session.user_id === userId || session.user_email === email) {
            await redis.del(key);
          }
        }
      }

      return NextResponse.json({ success: true, message: 'User disabled successfully' });
    }

    return NextResponse.json({ error: 'Invalid action or missing parameters' }, { status: 400 });
  } catch (error) {
    console.error('Failed to perform user action:', error);
    return NextResponse.json({ error: 'Failed to perform action' }, { status: 500 });
  }
}