import { NextRequest, NextResponse } from 'next/server';
import { PokerWebSocket } from '@/lib/websocket';

interface Table {
  id: number;
  name: string;
  code: string;
  type: '6p' | '9p';
  status: 'active' | 'inactive';
  bigBlind: number;
  smallBlind: number;
  minBuyIn: number;
  maxBuyIn: number;
  isVisible: boolean;
  expiredAt?: string;
  seated: number;
  maxPlayers: number;
  totalPot: number;
  totalHands: number;
}

// Convert WebSocket table format to admin table format
function convertWebSocketTable(wsTable: any): Table {
  return {
    id: wsTable.id,
    name: wsTable.n,
    code: wsTable.c,
    type: wsTable.t,
    status: 'active', // WebSocket tables are always active if visible
    bigBlind: wsTable.bb,
    smallBlind: wsTable.sb,
    minBuyIn: wsTable.nbi,
    maxBuyIn: wsTable.xbi,
    isVisible: wsTable.iv,
    expiredAt: wsTable.ea,
    seated: wsTable.sd,
    maxPlayers: wsTable.t === '6p' ? 6 : 9,
    totalPot: wsTable.tp,
    totalHands: wsTable.th,
  };
}

export async function GET(request: NextRequest) {
  try {
    // Get session token from request headers
    const sessionToken = request.headers.get('x-session-token') || request.cookies.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json({ error: 'No session token provided' }, { status: 401 });
    }

    // Create a temporary WebSocket connection to fetch tables
    const ws = new PokerWebSocket('admin-fetch');

    try {
      // Connect and authorize
      await ws.connect();

      // Fetch tables
      const tables = await new Promise<any[]>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Timeout waiting for tables'));
        }, 10000);

        ws.on('tables', (event) => {
          clearTimeout(timeout);
          resolve(event.tables || []);
        });

        ws.getTables();
      });

      // Convert to admin format
      const adminTables = tables.map(convertWebSocketTable);

      // Disconnect
      ws.disconnect();

      return NextResponse.json({ tables: adminTables });
    } catch (wsError) {
      console.error('WebSocket error:', wsError);
      ws.disconnect();

      // Fallback to mock data if WebSocket fails
      const mockTables: Table[] = [
        {
          id: 1,
          name: 'High Stakes Table',
          code: 'HIGH001',
          type: '6p',
          status: 'active',
          bigBlind: 100,
          smallBlind: 50,
          minBuyIn: 1000,
          maxBuyIn: 10000,
          isVisible: true,
          seated: 3,
          maxPlayers: 6,
          totalPot: 15000,
          totalHands: 45,
        },
        {
          id: 2,
          name: 'Beginner Table',
          code: 'BEGIN001',
          type: '9p',
          status: 'active',
          bigBlind: 10,
          smallBlind: 5,
          minBuyIn: 100,
          maxBuyIn: 1000,
          isVisible: true,
          seated: 7,
          maxPlayers: 9,
          totalPot: 2500,
          totalHands: 23,
        },
      ];

      return NextResponse.json({
        tables: mockTables,
        warning: 'Using fallback data - WebSocket connection failed'
      });
    }
  } catch (error) {
    console.error('Failed to fetch tables:', error);
    return NextResponse.json({ error: 'Failed to fetch tables' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, code, type, bigBlind, smallBlind, minBuyIn, maxBuyIn, isVisible, expiredAt } = body;

    // Validate required fields
    if (!name || !code || !type || !bigBlind || !smallBlind || !minBuyIn || !maxBuyIn) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Validate table type
    if (type !== '6p' && type !== '9p') {
      return NextResponse.json({ error: 'Invalid table type. Must be 6p or 9p' }, { status: 400 });
    }

    // Validate blinds and buy-ins
    if (bigBlind <= smallBlind) {
      return NextResponse.json({ error: 'Big blind must be greater than small blind' }, { status: 400 });
    }

    if (maxBuyIn <= minBuyIn) {
      return NextResponse.json({ error: 'Max buy-in must be greater than min buy-in' }, { status: 400 });
    }

    // In a real implementation, you would:
    // 1. Insert the table into the database
    // 2. Notify the Hamster server to reload tables
    // 3. Return the created table with its ID

    const newTable: Table = {
      id: Math.floor(Math.random() * 10000), // Mock ID generation
      name,
      code,
      type,
      status: 'active',
      bigBlind,
      smallBlind,
      minBuyIn,
      maxBuyIn,
      isVisible: isVisible ?? true,
      expiredAt,
      seated: 0,
      maxPlayers: type === '6p' ? 6 : 9,
      totalPot: 0,
      totalHands: 0,
    };

    return NextResponse.json({ table: newTable, message: 'Table created successfully' });
  } catch (error) {
    console.error('Failed to create table:', error);
    return NextResponse.json({ error: 'Failed to create table' }, { status: 500 });
  }
}
