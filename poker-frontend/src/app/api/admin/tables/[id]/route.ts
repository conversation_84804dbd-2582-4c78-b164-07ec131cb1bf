import { NextRequest, NextResponse } from 'next/server';

interface Table {
  id: number;
  name: string;
  code: string;
  type: '6p' | '9p';
  status: 'active' | 'inactive';
  bigBlind: number;
  smallBlind: number;
  minBuyIn: number;
  maxBuyIn: number;
  isVisible: boolean;
  expiredAt?: string;
  seated: number;
  maxPlayers: number;
  totalPot: number;
  totalHands: number;
}

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const tableId = parseInt(params.id);
    
    if (isNaN(tableId)) {
      return NextResponse.json({ error: 'Invalid table ID' }, { status: 400 });
    }

    // In a real implementation, fetch from database
    // For now, return mock data
    const mockTable: Table = {
      id: tableId,
      name: 'Sample Table',
      code: 'SAMPLE001',
      type: '6p',
      status: 'active',
      bigBlind: 100,
      smallBlind: 50,
      minBuyIn: 1000,
      maxBuyIn: 10000,
      isVisible: true,
      seated: 3,
      maxPlayers: 6,
      totalPot: 15000,
      totalHands: 45,
    };

    return NextResponse.json({ table: mockTable });
  } catch (error) {
    console.error('Failed to fetch table:', error);
    return NextResponse.json({ error: 'Failed to fetch table' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const tableId = parseInt(params.id);
    
    if (isNaN(tableId)) {
      return NextResponse.json({ error: 'Invalid table ID' }, { status: 400 });
    }

    const body = await request.json();
    const { name, code, type, bigBlind, smallBlind, minBuyIn, maxBuyIn, isVisible, expiredAt, status } = body;

    // Validate required fields
    if (!name || !code || !type || !bigBlind || !smallBlind || !minBuyIn || !maxBuyIn) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Validate table type
    if (type !== '6p' && type !== '9p') {
      return NextResponse.json({ error: 'Invalid table type. Must be 6p or 9p' }, { status: 400 });
    }

    // Validate status
    if (status && status !== 'active' && status !== 'inactive') {
      return NextResponse.json({ error: 'Invalid status. Must be active or inactive' }, { status: 400 });
    }

    // Validate blinds and buy-ins
    if (bigBlind <= smallBlind) {
      return NextResponse.json({ error: 'Big blind must be greater than small blind' }, { status: 400 });
    }

    if (maxBuyIn <= minBuyIn) {
      return NextResponse.json({ error: 'Max buy-in must be greater than min buy-in' }, { status: 400 });
    }

    // In a real implementation, you would:
    // 1. Update the table in the database
    // 2. Notify the Hamster server to reload tables
    // 3. Return the updated table

    const updatedTable: Table = {
      id: tableId,
      name,
      code,
      type,
      status: status || 'active',
      bigBlind,
      smallBlind,
      minBuyIn,
      maxBuyIn,
      isVisible: isVisible ?? true,
      expiredAt,
      seated: 3, // Mock data
      maxPlayers: type === '6p' ? 6 : 9,
      totalPot: 15000, // Mock data
      totalHands: 45, // Mock data
    };

    return NextResponse.json({ table: updatedTable, message: 'Table updated successfully' });
  } catch (error) {
    console.error('Failed to update table:', error);
    return NextResponse.json({ error: 'Failed to update table' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const tableId = parseInt(params.id);
    
    if (isNaN(tableId)) {
      return NextResponse.json({ error: 'Invalid table ID' }, { status: 400 });
    }

    // In a real implementation, you would:
    // 1. Check if table has active players
    // 2. Soft delete or mark as inactive in the database
    // 3. Notify the Hamster server to reload tables
    // 4. Possibly move players to other tables or lobby

    // For now, just return success
    return NextResponse.json({ message: 'Table deleted successfully' });
  } catch (error) {
    console.error('Failed to delete table:', error);
    return NextResponse.json({ error: 'Failed to delete table' }, { status: 500 });
  }
}
