'use client';

import { useState, useEffect } from 'react';
import AdminRoute from '@/components/AdminRoute';
import { useAuth } from '@/contexts/AuthContext';
import { usePoker } from '@/contexts/PokerContext';
import { Users, Activity, Settings, BarChart3, Plus, Edit, Trash2, Eye, EyeOff, FileText } from 'lucide-react';
import { logAdminAction, AUDIT_ACTIONS } from '@/lib/audit';

interface User {
  uid: string;
  email: string;
  displayName: string;
  creationTime: string;
  lastSignInTime: string;
  disabled: boolean;
}

interface Table {
  id: number;
  name: string;
  code: string;
  type: '6p' | '9p';
  status: 'active' | 'inactive';
  bigBlind: number;
  smallBlind: number;
  minBuyIn: number;
  maxBuyIn: number;
  isVisible: boolean;
  expiredAt?: string;
  seated: number;
  maxPlayers: number;
  totalPot: number;
  totalHands: number;
}

interface AnalyticsData {
  totalUsers: number;
  activeUsers: number;
  activeTables: number;
  totalTables: number;
  gamesPlayed: number;
  totalPot: number;
  avgGameDuration: number;
  peakConcurrentUsers: number;
  userGrowth: {
    daily: number;
    weekly: number;
    monthly: number;
  };
  tableUtilization: {
    sixPlayer: {
      total: number;
      active: number;
      utilization: number;
    };
    ninePlayer: {
      total: number;
      active: number;
      utilization: number;
    };
  };
  recentActivity: Array<{
    timestamp: string;
    type: 'user_join' | 'user_leave' | 'game_start' | 'game_end' | 'table_create';
    description: string;
  }>;
}

interface AuditLog {
  id: string;
  timestamp: string;
  adminEmail: string;
  action: string;
  target: string;
  details: string;
  ipAddress?: string;
  userAgent?: string;
}

export default function AdminPage() {
  const { user } = useAuth();
  const { tables: pokerTables, connected, getTables } = usePoker();
  const [users, setUsers] = useState<User[]>([]);
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [settings, setSettings] = useState<any>(null);
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('users');
  const [showCreateTable, setShowCreateTable] = useState(false);

  // Convert poker tables to admin table format
  const tables: Table[] = pokerTables.map(table => ({
    id: table.id,
    name: table.n,
    code: table.c,
    type: table.t,
    status: 'active' as const,
    bigBlind: table.bb,
    smallBlind: table.sb,
    minBuyIn: table.nbi,
    maxBuyIn: table.xbi,
    isVisible: table.iv,
    expiredAt: table.ea,
    seated: table.sd,
    maxPlayers: table.t === '6p' ? 6 : 9,
    totalPot: table.tp,
    totalHands: table.th,
  }));

  useEffect(() => {
    fetchUsers();
    fetchSettings();
    fetchAuditLogs();

    // Fetch tables via WebSocket if connected
    if (connected) {
      getTables();
    }
  }, [connected, getTables]);

  // Fetch analytics when tables data changes
  useEffect(() => {
    if (tables.length > 0) {
      fetchAnalytics();
    }
  }, [tables]);

  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/admin/users');

      if (response.ok) {
        const data = await response.json();
        setUsers(data.users);
      }
    } catch (error) {
      console.error('Failed to fetch users:', error);
    } finally {
      setLoading(false);
    }
  };



  const fetchAnalytics = async () => {
    try {
      // Pass table data to analytics API for real-time calculations
      const tablesParam = encodeURIComponent(JSON.stringify(tables));
      const response = await fetch(`/api/admin/analytics?tables=${tablesParam}`);

      if (response.ok) {
        const data = await response.json();
        setAnalytics(data);
      }
    } catch (error) {
      console.error('Failed to fetch analytics:', error);
    }
  };

  const fetchSettings = async () => {
    try {
      const response = await fetch('/api/admin/settings');

      if (response.ok) {
        const data = await response.json();
        setSettings(data.settings);
      }
    } catch (error) {
      console.error('Failed to fetch settings:', error);
    }
  };

  const fetchAuditLogs = async () => {
    try {
      const response = await fetch('/api/admin/audit');

      if (response.ok) {
        const data = await response.json();
        setAuditLogs(data.logs);
      }
    } catch (error) {
      console.error('Failed to fetch audit logs:', error);
    }
  };

  const handleUserAction = async (userId: string, email: string, action: 'enable' | 'disable') => {
    try {
      const response = await fetch('/api/admin/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action,
          userId,
          email,
        }),
      });

      if (response.ok) {
        // Log the admin action
        await logAdminAction({
          adminEmail: user?.email || 'unknown',
          action: action === 'disable' ? AUDIT_ACTIONS.USER_DISABLE : AUDIT_ACTIONS.USER_ENABLE,
          target: email,
          details: `User ${action}d by admin`,
        });

        // Refresh users list and audit logs
        fetchUsers();
        fetchAuditLogs();
      } else {
        console.error('Failed to perform user action');
      }
    } catch (error) {
      console.error('Failed to perform user action:', error);
    }
  };

  const handleSaveSettings = async () => {
    try {
      const response = await fetch('/api/admin/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          settings,
        }),
      });

      if (response.ok) {
        // Log the admin action
        await logAdminAction({
          adminEmail: user?.email || 'unknown',
          action: AUDIT_ACTIONS.SETTINGS_UPDATE,
          target: 'Server Settings',
          details: 'Server settings updated by admin',
        });

        alert('Settings saved successfully!');
        fetchSettings(); // Refresh settings
        fetchAuditLogs(); // Refresh audit logs
      } else {
        alert('Failed to save settings');
      }
    } catch (error) {
      console.error('Failed to save settings:', error);
      alert('Failed to save settings');
    }
  };

  return (
    <AdminRoute>
      <div className="min-h-screen bg-gray-900 text-white">
        <div className="container mx-auto px-4 py-8">
          <h1 className="text-3xl font-bold mb-8">Admin Portal</h1>
          
          {/* Tab Navigation */}
          <div className="flex space-x-1 mb-8">
            {[
              { id: 'users', label: 'Users', icon: Users },
              { id: 'tables', label: 'Tables', icon: Activity },
              { id: 'analytics', label: 'Analytics', icon: BarChart3 },
              { id: 'settings', label: 'Settings', icon: Settings },
              { id: 'audit', label: 'Audit Logs', icon: FileText },
            ].map(({ id, label, icon: Icon }) => (
              <button
                key={id}
                onClick={() => setActiveTab(id)}
                className={`flex items-center px-4 py-2 rounded-lg transition-colors ${
                  activeTab === id
                    ? 'bg-green-600 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                <Icon className="w-4 h-4 mr-2" />
                {label}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          {activeTab === 'users' && (
            <div className="bg-gray-800 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">User Management</h2>
              
              {loading ? (
                <div className="text-center py-8">Loading users...</div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full text-left">
                    <thead>
                      <tr className="border-b border-gray-700">
                        <th className="pb-3">Email</th>
                        <th className="pb-3">Display Name</th>
                        <th className="pb-3">Created</th>
                        <th className="pb-3">Last Sign In</th>
                        <th className="pb-3">Status</th>
                        <th className="pb-3">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {users.map((user) => (
                        <tr key={user.uid} className="border-b border-gray-700/50">
                          <td className="py-3">{user.email}</td>
                          <td className="py-3">{user.displayName || 'N/A'}</td>
                          <td className="py-3">
                            {new Date(user.creationTime).toLocaleDateString()}
                          </td>
                          <td className="py-3">
                            {user.lastSignInTime 
                              ? new Date(user.lastSignInTime).toLocaleDateString()
                              : 'Never'
                            }
                          </td>
                          <td className="py-3">
                            <span className={`px-2 py-1 rounded text-xs ${
                              user.disabled
                                ? 'bg-red-600 text-white'
                                : 'bg-green-600 text-white'
                            }`}>
                              {user.disabled ? 'Disabled' : 'Active'}
                            </span>
                          </td>
                          <td className="py-3">
                            <button
                              onClick={() => handleUserAction(user.uid, user.email, user.disabled ? 'enable' : 'disable')}
                              className={`px-3 py-1 rounded text-xs transition-colors ${
                                user.disabled
                                  ? 'bg-green-600 hover:bg-green-700 text-white'
                                  : 'bg-red-600 hover:bg-red-700 text-white'
                              }`}
                            >
                              {user.disabled ? 'Enable' : 'Disable'}
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          )}

          {activeTab === 'tables' && (
            <div className="bg-gray-800 rounded-lg p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold">Table Management</h2>
                <button
                  onClick={() => setShowCreateTable(true)}
                  className="flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg transition-colors"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Create Table
                </button>
              </div>

              {loading ? (
                <div className="text-center py-8">
                  <div className="text-gray-400">Loading tables...</div>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full text-left">
                    <thead>
                      <tr className="border-b border-gray-700">
                        <th className="py-3 pr-4">ID</th>
                        <th className="py-3 pr-4">Name</th>
                        <th className="py-3 pr-4">Code</th>
                        <th className="py-3 pr-4">Type</th>
                        <th className="py-3 pr-4">Blinds</th>
                        <th className="py-3 pr-4">Buy-in Range</th>
                        <th className="py-3 pr-4">Players</th>
                        <th className="py-3 pr-4">Status</th>
                        <th className="py-3 pr-4">Visibility</th>
                        <th className="py-3">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {tables.map((table) => (
                        <tr key={table.id} className="border-b border-gray-700">
                          <td className="py-3 pr-4">{table.id}</td>
                          <td className="py-3 pr-4">{table.name}</td>
                          <td className="py-3 pr-4">
                            <code className="bg-gray-700 px-2 py-1 rounded text-sm">
                              {table.code}
                            </code>
                          </td>
                          <td className="py-3 pr-4">
                            <span className={`px-2 py-1 rounded text-xs ${
                              table.type === '6p'
                                ? 'bg-blue-600 text-white'
                                : 'bg-purple-600 text-white'
                            }`}>
                              {table.type === '6p' ? '6 Player' : '9 Player'}
                            </span>
                          </td>
                          <td className="py-3 pr-4">
                            ${table.smallBlind}/${table.bigBlind}
                          </td>
                          <td className="py-3 pr-4">
                            ${table.minBuyIn} - ${table.maxBuyIn}
                          </td>
                          <td className="py-3 pr-4">
                            {table.seated}/{table.maxPlayers}
                          </td>
                          <td className="py-3 pr-4">
                            <span className={`px-2 py-1 rounded text-xs ${
                              table.status === 'active'
                                ? 'bg-green-600 text-white'
                                : 'bg-red-600 text-white'
                            }`}>
                              {table.status}
                            </span>
                          </td>
                          <td className="py-3 pr-4">
                            {table.isVisible ? (
                              <Eye className="w-4 h-4 text-green-400" />
                            ) : (
                              <EyeOff className="w-4 h-4 text-gray-400" />
                            )}
                          </td>
                          <td className="py-3">
                            <div className="flex space-x-2">
                              <button
                                className="p-1 text-blue-400 hover:text-blue-300"
                                title="Edit Table"
                              >
                                <Edit className="w-4 h-4" />
                              </button>
                              <button
                                className="p-1 text-red-400 hover:text-red-300"
                                title="Delete Table"
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>

                  {tables.length === 0 && (
                    <div className="text-center py-8">
                      <div className="text-gray-400">No tables found</div>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {activeTab === 'analytics' && (
            <div className="bg-gray-800 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-6">Analytics Dashboard</h2>

              {analytics ? (
                <div className="space-y-6">
                  {/* Key Metrics */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div className="bg-gray-700 p-4 rounded-lg">
                      <h3 className="text-lg font-semibold mb-2">Total Users</h3>
                      <p className="text-2xl font-bold text-green-400">{analytics.totalUsers}</p>
                      <p className="text-sm text-gray-400">Active: {analytics.activeUsers}</p>
                    </div>
                    <div className="bg-gray-700 p-4 rounded-lg">
                      <h3 className="text-lg font-semibold mb-2">Active Tables</h3>
                      <p className="text-2xl font-bold text-blue-400">{analytics.activeTables}</p>
                      <p className="text-sm text-gray-400">Total: {analytics.totalTables}</p>
                    </div>
                    <div className="bg-gray-700 p-4 rounded-lg">
                      <h3 className="text-lg font-semibold mb-2">Games Played</h3>
                      <p className="text-2xl font-bold text-purple-400">{analytics.gamesPlayed}</p>
                      <p className="text-sm text-gray-400">Avg: {analytics.avgGameDuration}min</p>
                    </div>
                    <div className="bg-gray-700 p-4 rounded-lg">
                      <h3 className="text-lg font-semibold mb-2">Total Pot</h3>
                      <p className="text-2xl font-bold text-yellow-400">${analytics.totalPot.toLocaleString()}</p>
                      <p className="text-sm text-gray-400">Peak Users: {analytics.peakConcurrentUsers}</p>
                    </div>
                  </div>

                  {/* User Growth */}
                  <div className="bg-gray-700 p-4 rounded-lg">
                    <h3 className="text-lg font-semibold mb-4">User Growth</h3>
                    <div className="grid grid-cols-3 gap-4">
                      <div className="text-center">
                        <p className="text-2xl font-bold text-green-400">{analytics.userGrowth.daily}</p>
                        <p className="text-sm text-gray-400">Daily</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-blue-400">{analytics.userGrowth.weekly}</p>
                        <p className="text-sm text-gray-400">Weekly</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-purple-400">{analytics.userGrowth.monthly}</p>
                        <p className="text-sm text-gray-400">Monthly</p>
                      </div>
                    </div>
                  </div>

                  {/* Table Utilization */}
                  <div className="bg-gray-700 p-4 rounded-lg">
                    <h3 className="text-lg font-semibold mb-4">Table Utilization</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <h4 className="font-medium mb-2">6-Player Tables</h4>
                        <div className="flex justify-between text-sm">
                          <span>Active: {analytics.tableUtilization.sixPlayer.active}</span>
                          <span>Total: {analytics.tableUtilization.sixPlayer.total}</span>
                        </div>
                        <div className="w-full bg-gray-600 rounded-full h-2 mt-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{ width: `${analytics.tableUtilization.sixPlayer.utilization}%` }}
                          ></div>
                        </div>
                        <p className="text-xs text-gray-400 mt-1">{analytics.tableUtilization.sixPlayer.utilization}% utilization</p>
                      </div>
                      <div>
                        <h4 className="font-medium mb-2">9-Player Tables</h4>
                        <div className="flex justify-between text-sm">
                          <span>Active: {analytics.tableUtilization.ninePlayer.active}</span>
                          <span>Total: {analytics.tableUtilization.ninePlayer.total}</span>
                        </div>
                        <div className="w-full bg-gray-600 rounded-full h-2 mt-2">
                          <div
                            className="bg-purple-600 h-2 rounded-full"
                            style={{ width: `${analytics.tableUtilization.ninePlayer.utilization}%` }}
                          ></div>
                        </div>
                        <p className="text-xs text-gray-400 mt-1">{analytics.tableUtilization.ninePlayer.utilization}% utilization</p>
                      </div>
                    </div>
                  </div>

                  {/* Recent Activity */}
                  <div className="bg-gray-700 p-4 rounded-lg">
                    <h3 className="text-lg font-semibold mb-4">Recent Activity</h3>
                    <div className="space-y-2">
                      {analytics.recentActivity.map((activity, index) => (
                        <div key={index} className="flex justify-between items-center py-2 border-b border-gray-600 last:border-b-0">
                          <div className="flex items-center space-x-3">
                            <span className={`w-2 h-2 rounded-full ${
                              activity.type === 'user_join' ? 'bg-green-400' :
                              activity.type === 'user_leave' ? 'bg-red-400' :
                              activity.type === 'game_start' ? 'bg-blue-400' :
                              activity.type === 'game_end' ? 'bg-purple-400' :
                              'bg-yellow-400'
                            }`}></span>
                            <span className="text-sm">{activity.description}</span>
                          </div>
                          <span className="text-xs text-gray-400">
                            {new Date(activity.timestamp).toLocaleTimeString()}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="text-gray-400">Loading analytics...</div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'settings' && (
            <div className="bg-gray-800 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-6">Server Settings</h2>

              <div className="space-y-6">
                {/* Game Settings */}
                <div className="bg-gray-700 p-4 rounded-lg">
                  <h3 className="text-lg font-semibold mb-4">Game Settings</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">Default Big Blind</label>
                      <input
                        type="number"
                        className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2"
                        defaultValue="100"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">Default Small Blind</label>
                      <input
                        type="number"
                        className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2"
                        defaultValue="50"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">Max Tables Per User</label>
                      <input
                        type="number"
                        className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2"
                        defaultValue="3"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">Game Timeout (minutes)</label>
                      <input
                        type="number"
                        className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2"
                        defaultValue="30"
                      />
                    </div>
                  </div>
                </div>

                {/* Server Settings */}
                <div className="bg-gray-700 p-4 rounded-lg">
                  <h3 className="text-lg font-semibold mb-4">Server Settings</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">Max Concurrent Users</label>
                      <input
                        type="number"
                        className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2"
                        defaultValue="1000"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">Session Timeout (hours)</label>
                      <input
                        type="number"
                        className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2"
                        defaultValue="24"
                      />
                    </div>
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="enableRegistration"
                        className="rounded"
                        defaultChecked
                      />
                      <label htmlFor="enableRegistration" className="text-sm font-medium">Enable Registration</label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="maintenanceMode"
                        className="rounded"
                      />
                      <label htmlFor="maintenanceMode" className="text-sm font-medium">Maintenance Mode</label>
                    </div>
                  </div>
                </div>

                {/* Security Settings */}
                <div className="bg-gray-700 p-4 rounded-lg">
                  <h3 className="text-lg font-semibold mb-4">Security Settings</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">Max Login Attempts</label>
                      <input
                        type="number"
                        className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2"
                        defaultValue="5"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">Lockout Duration (minutes)</label>
                      <input
                        type="number"
                        className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2"
                        defaultValue="15"
                      />
                    </div>
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="enableRateLimiting"
                        className="rounded"
                        defaultChecked
                      />
                      <label htmlFor="enableRateLimiting" className="text-sm font-medium">Enable Rate Limiting</label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="requireEmailVerification"
                        className="rounded"
                      />
                      <label htmlFor="requireEmailVerification" className="text-sm font-medium">Require Email Verification</label>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-4">
                  <button
                    onClick={handleSaveSettings}
                    className="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg transition-colors"
                  >
                    Save Settings
                  </button>
                  <button className="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors">
                    Reset to Defaults
                  </button>
                  <button className="px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors">
                    Restart Server
                  </button>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'audit' && (
            <div className="bg-gray-800 rounded-lg p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold">Audit Logs</h2>
                <button
                  onClick={() => {
                    if (confirm('Are you sure you want to clear all audit logs?')) {
                      fetch('/api/admin/audit?action=clear_all', { method: 'DELETE' })
                        .then(() => {
                          fetchAuditLogs();
                          logAdminAction({
                            adminEmail: user?.email || 'unknown',
                            action: AUDIT_ACTIONS.AUDIT_CLEAR,
                            target: 'Audit Logs',
                            details: 'All audit logs cleared by admin',
                          });
                        });
                    }
                  }}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors text-sm"
                >
                  Clear All Logs
                </button>
              </div>

              {loading ? (
                <div className="text-center py-8">
                  <div className="text-gray-400">Loading audit logs...</div>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full text-left">
                    <thead>
                      <tr className="border-b border-gray-700">
                        <th className="py-3 pr-4">Timestamp</th>
                        <th className="py-3 pr-4">Admin</th>
                        <th className="py-3 pr-4">Action</th>
                        <th className="py-3 pr-4">Target</th>
                        <th className="py-3 pr-4">Details</th>
                        <th className="py-3">IP Address</th>
                      </tr>
                    </thead>
                    <tbody>
                      {auditLogs.map((log) => (
                        <tr key={log.id} className="border-b border-gray-700">
                          <td className="py-3 pr-4">
                            {new Date(log.timestamp).toLocaleString()}
                          </td>
                          <td className="py-3 pr-4">{log.adminEmail}</td>
                          <td className="py-3 pr-4">
                            <span className={`px-2 py-1 rounded text-xs ${
                              log.action.includes('DISABLE') || log.action.includes('DELETE') || log.action.includes('CLEAR')
                                ? 'bg-red-600 text-white'
                                : log.action.includes('CREATE') || log.action.includes('ENABLE')
                                ? 'bg-green-600 text-white'
                                : log.action.includes('UPDATE')
                                ? 'bg-blue-600 text-white'
                                : 'bg-gray-600 text-white'
                            }`}>
                              {log.action}
                            </span>
                          </td>
                          <td className="py-3 pr-4">{log.target}</td>
                          <td className="py-3 pr-4">{log.details}</td>
                          <td className="py-3">{log.ipAddress || 'N/A'}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>

                  {auditLogs.length === 0 && (
                    <div className="text-center py-8">
                      <div className="text-gray-400">No audit logs found</div>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </AdminRoute>
  );
}